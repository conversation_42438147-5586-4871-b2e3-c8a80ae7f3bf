#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <deque>
#include <mutex>
#include <algorithm>

#include "rclcpp/rclcpp.hpp"
// #include "rclcpp_components/register_node_macro.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "cv_bridge/cv_bridge.h"

#include <nlohmann/json.hpp>

using namespace std::chrono_literals;

// namespace CacheWorker {

class SaveImageNode : public rclcpp::Node {
public:
    using ImageMsg = sensor_msgs::msg::Image;

    explicit SaveImageNode(const rclcpp::NodeOptions& options)
    : Node("save_image_node", options)
    {
        // 声明参数
        this->declare_parameter<std::vector<std::string>>("input_topics",
            {"/device_0_decode", "/device_1_decode", "/device_2_decode", "/device_3_decode"});
        this->declare_parameter<int>("max_queue_size", 1);

        // 获取参数
        this->get_parameter("input_topics", input_topics_);
        this->get_parameter("max_queue_size", max_queue_size_);

        // 限制输入topic数量
        if (input_topics_.size() > 8) {
            RCLCPP_WARN(this->get_logger(), "Too many input topics (>8), using only the first 8");
            input_topics_.resize(8);
        } else if (input_topics_.size() < 1) {
            RCLCPP_ERROR(this->get_logger(), "No input topics specified");
            return;
        }

        // 创建图像订阅者
        for (size_t i = 0; i < input_topics_.size(); ++i) {
            const auto& topic = input_topics_[i];
            auto callback = [this, i, topic](const ImageMsg::SharedPtr msg) {
                this->image_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<ImageMsg>(
                topic, 10, callback);

            image_subscribers_.push_back(sub);
            image_queues_[i] = std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic.c_str());
        }

        RCLCPP_INFO(this->get_logger(), "Vehicle detection node initialized with %zu cameras", input_topics_.size());
    }

    ~SaveImageNode() {
    }

private:
    void image_callback(const ImageMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            if (msg->encoding != "bgr8" && msg->encoding != "rgb8") {
                RCLCPP_ERROR(this->get_logger(), "Unsupported encoding: %s",
                                msg->encoding.c_str());
                return;
            }
            cv::Mat image(msg->height, msg->width, CV_8UC3,
                const_cast<uint8_t*>(msg->data.data()), msg->step);
            if (image.empty()) {
                RCLCPP_ERROR(this->get_logger(),
                                "Received empty image for camera: %d",
                                camera_idx);
                return;
            }
            if (msg->encoding == "rgb8") {
                cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                auto& queue = image_queues_[camera_idx];
                // 添加到对应相机的队列
                queue.push_back(std::make_pair(msg, image.clone()));
                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received image from %s, queue size: %zu",
                        topic.c_str(), image_queues_[camera_idx].size());

            // // 生产环境日志
            // LOGW("Received image from %s, timestamp of msg: %ld (ms), timestamp of system: %ld (ms)", 
            //         topic.c_str(), rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000,
            //         std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count());
            // LOGW("Delay between camera msg header and system time of getting image: %ld (ms)",
            //         std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count() - rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000);
            // LOGW("");
        }
        catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "CV bridge exception: %s", e.what());
        }
    }


private:
    // 参数
    std::vector<std::string> input_topics_;
    int max_queue_size_;

    // ROS2 接口
    std::vector<rclcpp::Subscription<ImageMsg>::SharedPtr> image_subscribers_;

    // 图像队列
    std::map<size_t, std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>> image_queues_;
    std::mutex queue_mutex_;
};

// } // namespace CacheWorker

// RCLCPP_COMPONENTS_REGISTER_NODE(CacheWorker::SaveImageNode)

int main(int argc, char * argv[]) {
    rclcpp::init(argc, argv);
    
    // 创建节点时提供 NodeOptions
    rclcpp::NodeOptions options;
    auto node = std::make_shared<SaveImageNode>(options);
    
    if (rclcpp::ok()) {
        rclcpp::spin(node);
    }
    rclcpp::shutdown();
    return 0;
}