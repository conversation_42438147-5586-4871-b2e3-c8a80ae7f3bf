#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <deque>
#include <queue>
#include <mutex>
#include <algorithm>
#include <thread>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <condition_variable>
#include <atomic>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_components/register_node_macro.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"

#include <nlohmann/json.hpp>

using namespace std::chrono_literals;

namespace CacheWorker {

class SaveImageNode : public rclcpp::Node {
public:
    using ImageMsg = sensor_msgs::msg::Image;

    explicit SaveImageNode(const rclcpp::NodeOptions& options)
    : Node("save_image_node", options)
    {
        // 声明参数
        this->declare_parameter<std::vector<std::string>>("input_topics",
            {"/device_0_decode", "/device_1_decode", "/device_2_decode", "/device_3_decode"});
        this->declare_parameter<int>("max_queue_size", 10);
        this->declare_parameter<std::string>("save_path", "/tmp/saved_images");
        this->declare_parameter<int>("retention_seconds", 90);

        // 获取参数
        this->get_parameter("input_topics", input_topics_);
        this->get_parameter("max_queue_size", max_queue_size_);
        this->get_parameter("save_path", save_path_);
        this->get_parameter("retention_seconds", retention_seconds_);

        // 创建保存目录
        try {
            std::filesystem::create_directories(save_path_);
            RCLCPP_INFO(this->get_logger(), "Image save directory: %s", save_path_.c_str());
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Failed to create save directory %s: %s",
                        save_path_.c_str(), e.what());
            return;
        }

        // 创建图像订阅者
        for (size_t i = 0; i < input_topics_.size(); ++i) {
            const auto& topic = input_topics_[i];
            auto callback = [this, i, topic](const ImageMsg::SharedPtr msg) {
                this->image_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<ImageMsg>(
                topic, 10, callback);

            image_subscribers_.push_back(sub);
            image_queues_[i] = std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic.c_str());
        }

        // 启动清理定时器 (每30秒检查一次)
        cleanup_timer_ = this->create_wall_timer(
            std::chrono::seconds(30),
            std::bind(&SaveImageNode::cleanup_old_images, this));

        // 启动异步保存线程
        save_thread_running_ = true;
        save_thread_ = std::thread(&SaveImageNode::image_save_worker, this);

        RCLCPP_INFO(this->get_logger(), "Vehicle detection node initialized with %zu cameras", input_topics_.size());
        RCLCPP_INFO(this->get_logger(), "Image retention period: %d seconds", retention_seconds_);
    }

    ~SaveImageNode() {
        // 停止保存线程
        save_thread_running_ = false;
        save_condition_.notify_all();
        if (save_thread_.joinable()) {
            save_thread_.join();
        }
    }

private:
    void image_callback(const ImageMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            if (msg->encoding != "bgr8" && msg->encoding != "rgb8") {
                RCLCPP_ERROR(this->get_logger(), "Unsupported encoding: %s",
                                msg->encoding.c_str());
                return;
            }
            cv::Mat image(msg->height, msg->width, CV_8UC3,
                const_cast<uint8_t*>(msg->data.data()), msg->step);
            if (image.empty()) {
                RCLCPP_ERROR(this->get_logger(),
                                "Received empty image for camera: %d",
                                camera_idx);
                return;
            }
            if (msg->encoding == "rgb8") {
                cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                auto& queue = image_queues_[camera_idx];
                // 添加到对应相机的队列
                queue.push_back(std::make_pair(msg, image.clone()));
                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received image from %s, queue size: %zu",
                        topic.c_str(), image_queues_[camera_idx].size());

            // 异步保存图像
            {
                std::lock_guard<std::mutex> lock(save_queue_mutex_);
                SaveImageTask task;
                task.image = image.clone();
                task.camera_idx = camera_idx;
                task.topic = topic;
                task.timestamp = std::chrono::system_clock::now();
                task.msg_timestamp_ms = rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000;

                save_queue_.push(task);
                save_condition_.notify_one();
            }

            // // 生产环境日志
            // LOGW("Received image from %s, timestamp of msg: %ld (ms), timestamp of system: %ld (ms)",
            //      topic.c_str(), rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000,
            //      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count());
            // LOGW("Delay between camera msg header and system time of getting image: %ld (ms)",
            //      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count() - rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000);
            // LOGW("");
        }
        catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "CV bridge exception: %s", e.what());
        }
    }

    // 生成基于ROS消息时间戳的文件名
    std::string generate_timestamp_filename(size_t camera_idx, int64_t msg_timestamp_ms) {
        // 将毫秒时间戳转换为时间结构
        time_t seconds = msg_timestamp_ms / 1000;
        int64_t ms = msg_timestamp_ms % 1000;

        std::stringstream ss;
        ss << std::put_time(std::localtime(&seconds), "%Y%m%d_%H%M%S");
        ss << "_" << std::setfill('0') << std::setw(3) << ms;
        ss << "_cam" << camera_idx << ".png";

        return ss.str();
    }

    // 异步图像保存工作线程
    void image_save_worker() {
        while (save_thread_running_) {
            std::unique_lock<std::mutex> lock(save_queue_mutex_);
            save_condition_.wait(lock, [this] {
                return !save_queue_.empty() || !save_thread_running_;
            });

            if (!save_thread_running_) {
                break;
            }

            while (!save_queue_.empty()) {
                SaveImageTask task = save_queue_.front();
                save_queue_.pop();
                lock.unlock();

                // 保存图像
                try {
                    std::string filename = generate_timestamp_filename(task.camera_idx, task.msg_timestamp_ms);
                    std::string filepath = save_path_ + "/" + filename;

                    if (cv::imwrite(filepath, task.image)) {
                        RCLCPP_DEBUG(this->get_logger(), "Saved image: %s", filename.c_str());
                    } else {
                        RCLCPP_ERROR(this->get_logger(), "Failed to save image: %s", filename.c_str());
                    }
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(this->get_logger(), "Exception while saving image: %s", e.what());
                }

                lock.lock();
            }
        }
    }

    // 清理旧图像
    void cleanup_old_images() {
        try {
            auto now = std::chrono::system_clock::now();
            auto cutoff_time = now - std::chrono::seconds(retention_seconds_);

            for (const auto& entry : std::filesystem::directory_iterator(save_path_)) {
                if (entry.is_regular_file() && entry.path().extension() == ".png") {
                    auto file_time = std::filesystem::last_write_time(entry);
                    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                        file_time - std::filesystem::file_time_type::clock::now() + now);

                    if (sctp < cutoff_time) {
                        std::filesystem::remove(entry.path());
                        RCLCPP_DEBUG(this->get_logger(), "Removed old image: %s",
                                    entry.path().filename().c_str());
                    }
                }
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error during cleanup: %s", e.what());
        }
    }

private:
    // 图像保存任务结构
    struct SaveImageTask {
        cv::Mat image;
        size_t camera_idx;
        std::string topic;
        std::chrono::system_clock::time_point timestamp;
        int64_t msg_timestamp_ms;  // ROS消息头时间戳(毫秒)
    };
    // 参数
    std::vector<std::string> input_topics_;
    int max_queue_size_;
    std::string save_path_;
    int retention_seconds_;

    // ROS2 接口
    std::vector<rclcpp::Subscription<ImageMsg>::SharedPtr> image_subscribers_;
    rclcpp::TimerBase::SharedPtr cleanup_timer_;

    // 图像队列
    std::map<size_t, std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>> image_queues_;
    std::mutex queue_mutex_;

    // 图像保存相关
    std::queue<SaveImageTask> save_queue_;
    std::mutex save_queue_mutex_;
    std::condition_variable save_condition_;
    std::thread save_thread_;
    std::atomic<bool> save_thread_running_{false};
};

} // namespace CacheWorker

RCLCPP_COMPONENTS_REGISTER_NODE(CacheWorker::SaveImageNode)
