#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <deque>
#include <queue>
#include <mutex>
#include <algorithm>
#include <thread>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <condition_variable>
#include <atomic>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_components/register_node_macro.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"

#include <nlohmann/json.hpp>

using namespace std::chrono_literals;

namespace CacheWorker {

class SaveImageNode : public rclcpp::Node {
public:
    using ImageMsg = sensor_msgs::msg::Image;
    static constexpr size_t MAX_SAVE_QUEUE_SIZE = 40; // 限制保存队列大小

    explicit SaveImageNode(const rclcpp::NodeOptions& options)
    : Node("save_image_node", options)
    {
        // 声明参数
        this->declare_parameter<std::vector<std::string>>("input_topics",
            {"/device_0_decode", "/device_1_decode", "/device_2_decode", "/device_3_decode"});
        this->declare_parameter<int>("max_queue_size", 10);
        this->declare_parameter<std::string>("save_path", "/ai_police/saved_images");
        this->declare_parameter<int>("retention_seconds", 90);

        // 获取参数
        this->get_parameter("input_topics", input_topics_);
        this->get_parameter("max_queue_size", max_queue_size_);
        this->get_parameter("save_path", save_path_);
        this->get_parameter("retention_seconds", retention_seconds_);

        // 创建保存目录
        try {
            std::filesystem::create_directories(save_path_);
            RCLCPP_INFO(this->get_logger(), "Image save directory: %s", save_path_.c_str());
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Failed to create save directory %s: %s",
                        save_path_.c_str(), e.what());
            return;
        }

        // 创建图像订阅者
        for (size_t i = 0; i < input_topics_.size(); ++i) {
            const auto& topic = input_topics_[i];
            auto callback = [this, i, topic](const ImageMsg::SharedPtr msg) {
                this->image_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<ImageMsg>(
                topic, 10, callback);

            image_subscribers_.push_back(sub);
            image_queues_[i] = std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic.c_str());
        }

        // 启动清理定时器 (每60秒检查一次，减少CPU开销)
        cleanup_timer_ = this->create_wall_timer(
            std::chrono::seconds(60),
            std::bind(&SaveImageNode::cleanup_old_images, this));

        // 启动异步保存线程
        save_thread_running_ = true;
        save_thread_ = std::thread(&SaveImageNode::image_save_worker, this);

        RCLCPP_INFO(this->get_logger(), "Save image node initialized with %zu cameras", input_topics_.size());
        RCLCPP_INFO(this->get_logger(), "Image retention period: %d seconds", retention_seconds_);
    }

    ~SaveImageNode() {
        // 停止保存线程
        save_thread_running_ = false;
        save_condition_.notify_all();
        if (save_thread_.joinable()) {
            save_thread_.join();
        }
    }

private:
    void image_callback(const ImageMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            if (msg->encoding != "bgr8" && msg->encoding != "rgb8") {
                RCLCPP_ERROR(this->get_logger(), "Unsupported encoding: %s",
                                msg->encoding.c_str());
                return;
            }
            cv::Mat image(msg->height, msg->width, CV_8UC3,
                const_cast<uint8_t*>(msg->data.data()), msg->step);
            if (image.empty()) {
                RCLCPP_ERROR(this->get_logger(),
                                "Received empty image for camera: %d",
                                camera_idx);
                return;
            }
            if (msg->encoding == "rgb8") {
                cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                auto& queue = image_queues_[camera_idx];
                // 添加到对应相机的队列
                queue.push_back(std::make_pair(msg, image.clone()));
                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received image from %s, queue size: %zu",
                        topic.c_str(), image_queues_[camera_idx].size());

            // 异步保存图像 - 优化内存使用
            {
                std::lock_guard<std::mutex> lock(save_queue_mutex_);

                // 限制保存队列大小，防止内存泄漏
                if (save_queue_.size() >= MAX_SAVE_QUEUE_SIZE) {
                    RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                        "Save queue full (%zu), dropping oldest image", save_queue_.size());
                    save_queue_.pop(); // 丢弃最旧的图像
                }

                SaveImageTask task;
                // 使用移动语义减少内存拷贝
                task.image = std::move(image.clone());
                task.camera_idx = camera_idx;
                task.topic = topic;
                task.timestamp = std::chrono::system_clock::now();
                task.msg_timestamp_ms = rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000;

                save_queue_.emplace(std::move(task));
                save_condition_.notify_one();
            }

            // // 生产环境日志
            // LOGW("Received image from %s, timestamp of msg: %ld (ms), timestamp of system: %ld (ms)",
            //      topic.c_str(), rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000,
            //      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count());
            // LOGW("Delay between camera msg header and system time of getting image: %ld (ms)",
            //      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count() - rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000);
            // LOGW("");
        }
        catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "CV bridge exception: %s", e.what());
        }
    }

    // 生成基于topic名称和UTC时间戳的文件名
    std::string generate_timestamp_filename(const std::string& topic, int64_t msg_timestamp_ms) {
        // 提取topic名称，去掉前导的"/"
        std::string topic_name = topic;
        if (topic_name.front() == '/') {
            topic_name = topic_name.substr(1);
        }

        // 生成文件名: {topic_name}_{utc_timestamp_ms}.png
        std::stringstream ss;
        ss << topic_name << "_" << msg_timestamp_ms << ".png";

        return ss.str();
    }

    // 优化的异步图像保存工作线程
    void image_save_worker() {
        while (save_thread_running_) {
            std::unique_lock<std::mutex> lock(save_queue_mutex_);

            // 使用超时等待，避免无限阻塞
            save_condition_.wait_for(lock, std::chrono::milliseconds(1000), [this] {
                return !save_queue_.empty() || !save_thread_running_;
            });

            if (!save_thread_running_) {
                break;
            }

            // 批量处理图像以减少锁竞争
            std::queue<SaveImageTask> batch_queue;
            const size_t BATCH_SIZE = 10; // 批量处理大小
            size_t processed = 0;

            while (!save_queue_.empty() && processed < BATCH_SIZE) {
                batch_queue.emplace(std::move(save_queue_.front()));
                save_queue_.pop();
                processed++;
            }
            lock.unlock();

            // 处理批量图像（无锁状态）
            while (!batch_queue.empty()) {
                SaveImageTask task = std::move(batch_queue.front());
                batch_queue.pop();

                try {
                    std::string filename = generate_timestamp_filename(task.topic, task.msg_timestamp_ms);
                    std::string filepath = save_path_ + "/" + filename;

                    if (cv::imwrite(filepath, task.image)) {
                        RCLCPP_INFO(this->get_logger(), "Saved image: %s", filename.c_str());
                    } else {
                        RCLCPP_ERROR(this->get_logger(), "Failed to save image: %s", filename.c_str());
                    }
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(this->get_logger(), "Exception while saving image: %s", e.what());
                }

                // 显式释放图像内存
                task.image.release();
            }
        }

        // 清理剩余队列
        std::lock_guard<std::mutex> lock(save_queue_mutex_);
        while (!save_queue_.empty()) {
            save_queue_.front().image.release();
            save_queue_.pop();
        }
    }

    // 优化的清理旧图像函数
    void cleanup_old_images() {
        try {
            // 检查目录是否存在
            if (!std::filesystem::exists(save_path_)) {
                return;
            }

            auto now = std::chrono::system_clock::now();
            auto cutoff_time = now - std::chrono::seconds(retention_seconds_);

            std::vector<std::filesystem::path> files_to_remove;
            files_to_remove.reserve(100); // 预分配空间

            // 收集需要删除的文件
            for (const auto& entry : std::filesystem::directory_iterator(save_path_)) {
                if (!entry.is_regular_file() || entry.path().extension() != ".png") {
                    continue;
                }

                try {
                    auto file_time = std::filesystem::last_write_time(entry);
                    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                        file_time - std::filesystem::file_time_type::clock::now() + now);

                    if (sctp < cutoff_time) {
                        files_to_remove.push_back(entry.path());
                    }
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "Error checking file time for %s: %s",
                               entry.path().c_str(), e.what());
                }
            }

            // 批量删除文件
            size_t removed_count = 0;
            for (const auto& file_path : files_to_remove) {
                try {
                    if (std::filesystem::remove(file_path)) {
                        removed_count++;
                    }
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "Error removing file %s: %s",
                               file_path.c_str(), e.what());
                }
            }

            if (removed_count > 0) {
                RCLCPP_INFO(this->get_logger(), "Cleaned up %zu old images", removed_count);
            }

        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error during cleanup: %s", e.what());
        }
    }

private:
    // 优化的图像保存任务结构
    struct SaveImageTask {
        cv::Mat image;
        size_t camera_idx;
        std::string topic;
        std::chrono::system_clock::time_point timestamp;
        int64_t msg_timestamp_ms;  // ROS消息头时间戳(毫秒)

        // 移动构造函数和赋值操作符
        SaveImageTask() = default;
        SaveImageTask(SaveImageTask&& other) noexcept
            : image(std::move(other.image))
            , camera_idx(other.camera_idx)
            , topic(std::move(other.topic))
            , timestamp(other.timestamp)
            , msg_timestamp_ms(other.msg_timestamp_ms) {}

        SaveImageTask& operator=(SaveImageTask&& other) noexcept {
            if (this != &other) {
                image = std::move(other.image);
                camera_idx = other.camera_idx;
                topic = std::move(other.topic);
                timestamp = other.timestamp;
                msg_timestamp_ms = other.msg_timestamp_ms;
            }
            return *this;
        }

        // 禁用拷贝构造和拷贝赋值
        SaveImageTask(const SaveImageTask&) = delete;
        SaveImageTask& operator=(const SaveImageTask&) = delete;
    };
    // 参数
    std::vector<std::string> input_topics_;
    int max_queue_size_;
    std::string save_path_;
    int retention_seconds_;

    // ROS2 接口
    std::vector<rclcpp::Subscription<ImageMsg>::SharedPtr> image_subscribers_;
    rclcpp::TimerBase::SharedPtr cleanup_timer_;

    // 图像队列
    std::map<size_t, std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>> image_queues_;
    std::mutex queue_mutex_;

    // 图像保存相关
    std::queue<SaveImageTask> save_queue_;
    std::mutex save_queue_mutex_;
    std::condition_variable save_condition_;
    std::thread save_thread_;
    std::atomic<bool> save_thread_running_{false};
};

} // namespace CacheWorker

RCLCPP_COMPONENTS_REGISTER_NODE(CacheWorker::SaveImageNode)
